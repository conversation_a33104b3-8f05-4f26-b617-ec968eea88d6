{"data_mtime": 1752652011, "dep_lines": [6, 1, 5, 7, 2, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.ext.asyncio", "collections.abc", "fastapi_users.db", "sqlalchemy.orm", "os", "<PERSON><PERSON><PERSON>", "builtins", "_frozen_importlib", "abc", "fastapi.param_functions", "fastapi_users", "fastapi_users.db.base", "fastapi_users_db_sqlalchemy", "sqlalchemy", "sqlalchemy.engine", "sqlalchemy.engine.url", "sqlalchemy.ext", "sqlalchemy.ext.asyncio.base", "sqlalchemy.ext.asyncio.engine", "sqlalchemy.ext.asyncio.session", "sqlalchemy.inspection", "sqlalchemy.orm.decl_api", "sqlalchemy.util", "sqlalchemy.util._py_collections", "typing"], "hash": "e375ecbe7ddee8331b461b4b1d3f9fc32b8dbecc", "id": "app.db", "ignore_all": false, "interface_hash": "4e00eb8e56203ebc89e51985b567a578943127c7", "mtime": 1752652008, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Local\\Projects\\BioCleaning\\app\\db.py", "plugin_data": null, "size": 1139, "suppressed": [], "version_id": "1.15.0"}