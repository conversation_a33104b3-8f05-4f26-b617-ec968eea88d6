{".class": "MypyFile", "_fullname": "app.db", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AsyncGenerator": {".class": "SymbolTableNode", "cross_ref": "typing.AsyncGenerator", "kind": "Gdef"}, "AsyncSession": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.ext.asyncio.session.AsyncSession", "kind": "Gdef"}, "Base": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.orm.decl_api.DeclarativeBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "app.db.Base", "name": "Base", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "app.db.Base", "has_param_spec_type": false, "metaclass_type": "sqlalchemy.orm.decl_api.DeclarativeAttributeIntercept", "metadata": {}, "module_name": "app.db", "mro": ["app.db.Base", "sqlalchemy.orm.decl_api.DeclarativeBase", "sqlalchemy.inspection.Inspectable", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.db.Base.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "app.db.Base", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DATABASE_URL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "app.db.DATABASE_URL", "name": "DATABASE_URL", "type": "builtins.str"}}, "DeclarativeBase": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.decl_api.DeclarativeBase", "kind": "Gdef"}, "Depends": {".class": "SymbolTableNode", "cross_ref": "fastapi.param_functions.Depends", "kind": "Gdef"}, "SQLAlchemyBaseUserTableUUID": {".class": "SymbolTableNode", "cross_ref": "fastapi_users_db_sqlalchemy.SQLAlchemyBaseUserTableUUID", "kind": "Gdef"}, "SQLAlchemyUserDatabase": {".class": "SymbolTableNode", "cross_ref": "fastapi_users_db_sqlalchemy.SQLAlchemyUserDatabase", "kind": "Gdef"}, "User": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["fastapi_users_db_sqlalchemy.SQLAlchemyBaseUserTableUUID", "app.db.Base"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "app.db.User", "name": "User", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "app.db.User", "has_param_spec_type": false, "metaclass_type": "sqlalchemy.orm.decl_api.DeclarativeAttributeIntercept", "metadata": {}, "module_name": "app.db", "mro": ["app.db.User", "fastapi_users_db_sqlalchemy.SQLAlchemyBaseUserTableUUID", "fastapi_users_db_sqlalchemy.SQLAlchemyBaseUserTable", "app.db.Base", "sqlalchemy.orm.decl_api.DeclarativeBase", "sqlalchemy.inspection.Inspectable", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.db.User.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "app.db.User", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.db.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.db.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.db.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.db.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.db.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.db.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "async_session_maker": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.db.async_session_maker", "name": "async_session_maker", "type": {".class": "Instance", "args": ["sqlalchemy.ext.asyncio.session.AsyncSession"], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.session.async_sessionmaker"}}}, "async_sessionmaker": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.ext.asyncio.session.async_sessionmaker", "kind": "Gdef"}, "create_async_engine": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.ext.asyncio.engine.create_async_engine", "kind": "Gdef"}, "create_db_and_tables": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "app.db.create_db_and_tables", "name": "create_db_and_tables", "type": null}}, "engine": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.db.engine", "name": "engine", "type": "sqlalchemy.ext.asyncio.engine.AsyncEngine"}}, "get_async_session": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_generator", "is_coroutine", "is_async_generator"], "fullname": "app.db.get_async_session", "name": "get_async_session", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_async_session", "ret_type": {".class": "Instance", "args": ["sqlalchemy.ext.asyncio.session.AsyncSession", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.AsyncGenerator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_user_db": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1], "arg_names": ["session"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_generator", "is_coroutine", "is_async_generator"], "fullname": "app.db.get_user_db", "name": "get_user_db", "type": {".class": "CallableType", "arg_kinds": [1], "arg_names": ["session"], "arg_types": ["sqlalchemy.ext.asyncio.session.AsyncSession"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_user_db", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}}, "path": "C:\\Local\\Projects\\BioCleaning\\app\\db.py"}